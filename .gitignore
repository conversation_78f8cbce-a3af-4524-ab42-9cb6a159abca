# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/
/.swc/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env.local
.env.development.local
.env.test.local
.env.production.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.idea/
*.sublime-*

# Supabase
.supabase/

# Logs
logs
*.log

# Storybook
storybook-static/

# Added by Claude Task Master
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Ignore generated Prisma client files
src/generated/prisma/

# Ignore specific script with potentially sensitive information
scripts/apply-migrations-supabase.ts

# Repomix outputs
repomix-*.xml
repomix.config.json

# Prompt library - personal AI prompts
prompt-library/
