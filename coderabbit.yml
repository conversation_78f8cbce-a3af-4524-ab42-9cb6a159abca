# CodeRabbit Configuration - Focused Code Review
# Only reviews core development files and directories

# Core settings
language: "en-US"
release_notes: false
ignore_draft_pr: true
enable_beta_features: true
disable_poem: true

# Reviews configuration
reviews:
  # Only include essential code files
  path_filters:
    # Include core application code
    - "src/**/*.ts"
    - "src/**/*.tsx" 
    - "src/**/*.js"
    - "src/**/*.jsx"
    - "prisma/schema.prisma"
    - "prisma/seed.ts"
    - "package.json"
    - "tsconfig.json"
    - "next.config.ts"
    - "middleware.ts"
    - "tailwind.config.js"
    - "postcss.config.mjs"
    - "vitest.config.ts"
    - "vitest.integration.config.ts"
    - "components.json"
    - "biome.json"
    
    # Include Google Apps Script files
    - "scripts/**/*.gs"
    - "scripts/**/*.ts"
    - "scripts/**/*.js"
    
    # Include Supabase functions
    - "supabase/functions/**/*.ts"
    - "supabase/migrations/**/*.sql"
    
    # Exclude everything else explicitly
    - "!**/*.md"
    - "!**/*.mdc" 
    - "!**/*.xml"
    - "!**/*.yml"
    - "!**/*.yaml"
    - "!**/*.json" # Exclude most JSON except specific ones above
    - "!**/*.css"
    - "!**/*.scss"
    - "!**/*.sass"
    - "!**/*.html"
    - "!**/*.svg"
    - "!**/*.png"
    - "!**/*.jpg"
    - "!**/*.jpeg"
    - "!**/*.gif"
    - "!**/*.ico"
    - "!**/*.woff"
    - "!**/*.woff2"
    - "!**/*.ttf"
    - "!**/*.eot"
    - "!**/*.pdf"
    - "!**/*.zip"
    - "!**/*.tar"
    - "!**/*.gz"
    
    # Exclude generated and build files
    - "!src/generated/**"
    - "!**/node_modules/**"
    - "!dist/**"
    - "!build/**"
    - "!.next/**"
    - "!coverage/**"
    - "!.turbo/**"
    
    # Exclude test files (focus on production code)
    - "!**/*.test.ts"
    - "!**/*.test.tsx"
    - "!**/*.spec.ts"
    - "!**/*.spec.tsx"
    - "!**/__tests__/**"
    - "!**/tests/**"
    
    # Exclude documentation and config directories
    - "!docs/**"
    - "!.github/**"
    - "!.vscode/**"
    - "!.cursor/**"
    - "!memory-bank/**"
    - "!prompt-library/**"
    - "!templates/**"
    - "!upload/**"
    - "!migrations/**"
    - "!ai copy/**"
    
    # Exclude migration SQL files (auto-generated)
    - "!prisma/migrations/**/*.sql"
    
    # Exclude lock files and logs
    - "!pnpm-lock.yaml"
    - "!package-lock.json"
    - "!yarn.lock"
    - "!**/*.log"

  # File-specific review instructions
  path_instructions:
    - path: "src/**/*.{ts,tsx}"
      instructions: |
        Focus on TypeScript/React code quality:
        - Ensure proper type safety and avoid 'any' types
        - Check for proper error handling and async/await patterns
        - Verify React hooks usage and component patterns
        - Review for security vulnerabilities and performance issues
        - Ensure proper JSDoc comments for functions and complex logic
        - Check for proper imports and exports
        - Verify proper state management patterns

    - path: "prisma/**"
      instructions: |
        Review database schema and Prisma code:
        - Verify schema relationships and constraints are properly defined
        - Check for proper indexing and performance considerations
        - Ensure proper data types and field constraints
        - Review seed files for data integrity and security
        - Check for proper error handling in database operations

    - path: "supabase/functions/**/*.ts"
      instructions: |
        Review Supabase Edge Functions:
        - Ensure proper error handling and response formatting
        - Check for security best practices (input validation, sanitization)
        - Verify proper async/await patterns and database connections
        - Review for performance optimizations
        - Ensure proper CORS and authentication handling

    - path: "scripts/**/*.{gs,ts,js}"
      instructions: |
        Review Google Apps Script and automation code:
        - Check for proper error handling and logging
        - Verify API integration patterns and security
        - Ensure proper data validation and sanitization
        - Review for performance and rate limiting considerations
        - Check for proper credential management

    - path: "{package.json,tsconfig.json,*.config.*}"
      instructions: |
        Review configuration files:
        - Check for security vulnerabilities in dependencies
        - Ensure proper version pinning where appropriate
        - Verify build and script configurations
        - Review for performance optimizations

# Ignore patterns
ignore:
  # Ignore PR titles
  title_keywords:
    - "WIP"
    - "Draft"
    - "Test"
    - "Experiment"
    - "Temp"
    
  # Ignore specific branches
  branches:
    - "dev/test"
    - "experimental/*"
    - "temp/*"
    - "personal/*"
    
  # Ignore specific file patterns (additional safety net)
  files:
    - "**/*.md"
    - "**/*.xml"
    - "**/*.yml"
    - "**/*.yaml"
    - "**/README*"
    - "**/CHANGELOG*"
    - "**/.env*"
    - "**/LICENSE*"