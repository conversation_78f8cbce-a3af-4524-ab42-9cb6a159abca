{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["**/node_modules/**", "**/dist/**", "**/build/**", "**/.next/**", "**/coverage/**", "**/.turbo/**", "**/generated/**", "**/*.min.js", "**/*.bundle.js", "scripts/**", "upload/**", "prisma/seed.ts"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "attributePosition": "auto", "useEditorconfig": true}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "all": false, "a11y": {"noAccessKey": "error", "noAriaHiddenOnFocusable": "error", "noAriaUnsupportedElements": "error", "noAutofocus": "error", "noBlankTarget": "error", "noDistractingElements": "error", "noHeaderScope": "error", "noInteractiveElementToNoninteractiveRole": "error", "noLabelWithoutControl": "error", "noNoninteractiveElementToInteractiveRole": "error", "noNoninteractiveTabindex": "error", "noPositiveTabindex": "error", "noRedundantAlt": "error", "noRedundantRoles": "error", "noSvgWithoutTitle": "error", "useAltText": "error", "useAnchorContent": "error", "useAriaActivedescendantWithTabindex": "error", "useAriaPropsForRole": "error", "useButtonType": "error", "useFocusableInteractive": "error", "useGenericFontNames": "error", "useHeadingContent": "error", "useHtmlLang": "error", "useIframeTitle": "error", "useKeyWithClickEvents": "error", "useKeyWithMouseEvents": "error", "useMediaCaption": "error", "useSemanticElements": "error", "useValidAnchor": "error", "useValidAriaProps": "error", "useValidAriaRole": "error", "useValidAriaValues": "error", "useValidLang": "error"}, "complexity": {"noBannedTypes": "error", "noEmptyTypeParameters": "error", "noExcessiveCognitiveComplexity": "warn", "noExcessiveNestedTestSuites": "error", "noExtraBooleanCast": "error", "noForEach": "error", "noMultipleSpacesInRegularExpressionLiterals": "error", "noStaticOnlyClass": "error", "noThisInStatic": "error", "noUselessCatch": "error", "noUselessConstructor": "error", "noUselessEmptyExport": "error", "noUselessFragments": "error", "noUselessLabel": "error", "noUselessLoneBlockStatements": "error", "noUselessRename": "error", "noUselessStringConcat": "error", "noUselessSwitchCase": "error", "noUselessTernary": "error", "noUselessThisAlias": "error", "noUselessTypeConstraint": "error", "noUselessUndefinedInitialization": "error", "noVoid": "error", "noWith": "error", "useArrowFunction": "error", "useDateNow": "error", "useFlatMap": "error", "useLiteralKeys": "error", "useOptionalChain": "error", "useRegexLiterals": "error", "useSimpleNumberKeys": "error", "useSimplifiedLogicExpression": "error"}, "correctness": {"noChildrenProp": "error", "noConstAssign": "error", "noConstantCondition": "error", "noConstantMathMinMaxClamp": "error", "noConstructorReturn": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noFlatMapIdentity": "error", "noGlobalObjectCalls": "error", "noInnerDeclarations": "error", "noInvalidBuiltinInstantiation": "error", "noInvalidConstructorSuper": "error", "noInvalidDirectionInLinearGradient": "error", "noInvalidGridAreas": "error", "noInvalidNewBuiltin": "error", "noInvalidPositionAtImportRule": "error", "noInvalidUseBeforeDeclaration": "error", "noNewSymbol": "error", "noNodejsModules": "off", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noRenderReturnValue": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noStringCaseMismatch": "error", "noSwitchDeclarations": "error", "noUndeclaredDependencies": "error", "noUndeclaredVariables": "error", "noUnknownFunction": "error", "noUnknownMediaFeatureName": "error", "noUnknownProperty": "error", "noUnknownUnit": "error", "noUnmatchableAnbSelector": "error", "noUnnecessaryContinue": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedFunctionParameters": "error", "noUnusedImports": "error", "noUnusedLabels": "error", "noUnusedPrivateClassMembers": "error", "noUnusedVariables": "error", "noVoidElementsWithChildren": "error", "noVoidTypeReturn": "error", "useArrayLiterals": "error", "useExhaustiveDependencies": "error", "useHookAtTopLevel": "error", "useImportExtensions": "off", "useIsNan": "error", "useJsxKeyInIterable": "error", "useValidForDirection": "error", "useYield": "error"}, "performance": {"noAccumulatingSpread": "error", "noBarrelFile": "warn", "noDelete": "error", "noReExportAll": "warn", "useTopLevelRegex": "error"}, "security": {"noDangerouslySetInnerHtml": "error", "noDangerouslySetInnerHtmlWithChildren": "error", "noGlobalEval": "error"}, "style": {"noArguments": "error", "noCommaOperator": "error", "noDefaultExport": "off", "noDoneCallback": "error", "noImplicitBoolean": "error", "noInferrableTypes": "error", "noNamespace": "error", "noNamespaceImport": "off", "noNegationElse": "error", "noNonNullAssertion": "warn", "noParameterAssign": "error", "noParameterProperties": "off", "noRestrictedGlobals": "off", "noShoutyConstants": "error", "noUnusedTemplateLiteral": "error", "noUselessElse": "error", "noVar": "error", "noYodaExpression": "error", "useAsConstAssertion": "error", "useBlockStatements": "error", "useCollapsedElseIf": "error", "useConsistentArrayType": "error", "useConsistentBuiltinInstantiation": "error", "useConst": "error", "useDefaultParameterLast": "error", "useDefaultSwitchClause": "error", "useEnumInitializers": "error", "useExplicitLengthCheck": "error", "useExponentiationOperator": "error", "useExportType": "error", "useFilenamingConvention": "off", "useForOf": "error", "useFragmentSyntax": "error", "useImportType": "error", "useLiteralEnumMembers": "error", "useNamingConvention": "off", "useNodeAssertStrict": "error", "useNodejsImportProtocol": "error", "useNumberNamespace": "error", "useNumericLiterals": "error", "useSelfClosingElements": "error", "useShorthandArrayType": "error", "useShorthandAssign": "error", "useShorthandFunctionType": "error", "useSingleCaseStatement": "error", "useSingleVarDeclarator": "error", "useTemplate": "error", "useThrowNewError": "error", "useThrowOnlyError": "error", "useWhile": "error"}, "suspicious": {"noApproximativeNumericConstant": "error", "noArrayIndexKey": "error", "noAssignInExpressions": "error", "noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCommentText": "error", "noCompareNegZero": "error", "noConfusingLabels": "error", "noConfusingVoidType": "error", "noConsole": "warn", "noConsoleLog": "warn", "noConstEnum": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDoubleEquals": "error", "noDuplicateAtImportRules": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateFontNames": "error", "noDuplicateJsxProps": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noDuplicateSelectorsKeyframeBlock": "error", "noDuplicateTestHooks": "error", "noEmptyBlock": "error", "noEmptyInterface": "error", "noEvolvingTypes": "error", "noExplicitAny": "error", "noExportsInTest": "error", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFocusedTests": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noGlobalIsFinite": "error", "noGlobalIsNan": "error", "noImplicitAnyLet": "error", "noImportAssign": "error", "noImportantInKeyframe": "error", "noLabelVar": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noMisplacedAssertion": "error", "noMisrefactoredShorthandAssign": "error", "noPrototypeBuiltins": "error", "noReactSpecificProps": "off", "noRedeclare": "error", "noRedundantUseStrict": "error", "noSelfCompare": "error", "noShadowRestrictedNames": "error", "noShorthandPropertyOverrides": "error", "noSkippedTests": "error", "noSparseArray": "error", "noSuspiciousSemicolonInJsx": "error", "noThenProperty": "error", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "useAwait": "error", "useDefaultSwitchClauseLast": "error", "useErrorMessage": "error", "useGetterReturn": "error", "useIsArray": "error", "useNamespaceKeyword": "error", "useNumberToFixedDigitsArgument": "error", "useValidTypeof": "error"}, "nursery": {"noCommonJs": "off", "noDuplicateElseIf": "error", "noDynamicNamespaceImportAccess": "error", "noEnum": "off", "noExportedImports": "error", "noIrregularWhitespace": "error", "noRestrictedImports": "off", "noRestrictedTypes": "off", "noSecrets": "warn", "noUselessEscapeInRegex": "error", "useAdjacentOverloadSignatures": "error", "useAtIndex": "error", "useCollapsedIf": "error", "useConsistentMemberAccessibility": "error", "useExplicitType": "off", "useGuardForIn": "error", "useTrimStartEnd": "error"}}}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "attributePosition": "auto"}, "globals": ["console", "process", "<PERSON><PERSON><PERSON>", "global", "__dirname", "__filename", "vi", "describe", "it", "test", "expect", "beforeEach", "after<PERSON>ach", "beforeAll", "afterAll"]}, "json": {"parser": {"allowComments": true, "allowTrailingCommas": false}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}}, "css": {"formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "quoteStyle": "double"}, "linter": {"enabled": true}}, "overrides": [{"include": ["*.test.ts", "*.test.tsx", "*.spec.ts", "*.spec.tsx", "**/__tests__/**/*", "**/tests/**/*", "src/tests/**/*"], "linter": {"rules": {"suspicious": {"noConsole": "off", "noConsoleLog": "off", "noExplicitAny": "off"}, "style": {"noNonNullAssertion": "off"}, "nursery": {"noSecrets": "off"}, "complexity": {"noExcessiveCognitiveComplexity": "off"}, "correctness": {"noUnusedVariables": "warn", "noUnusedImports": "warn"}}}}, {"include": ["*.config.js", "*.config.ts", "*.config.mjs"], "linter": {"rules": {"style": {"noDefaultExport": "off"}, "correctness": {"noNodejsModules": "off"}}}}, {"include": ["src/app/(auth)/**/*", "src/app/auth/**/*"], "linter": {"rules": {"complexity": {"noExcessiveCognitiveComplexity": "warn"}, "suspicious": {"useAwait": "warn"}, "style": {"noNonNullAssertion": "warn"}}}}, {"include": ["src/components/common/**/*"], "linter": {"rules": {"complexity": {"noExcessiveCognitiveComplexity": "warn"}, "correctness": {"noUnusedVariables": "warn"}}}}, {"include": ["src/types/**/*", "types/**/*", "**/*.d.ts"], "linter": {"rules": {"style": {"noNamespace": "off"}, "suspicious": {"noEmptyInterface": "off"}, "nursery": {"noExportedImports": "warn"}}}}, {"include": ["src/app/api/**/*"], "linter": {"rules": {"nursery": {"noSecrets": "off"}}}}, {"include": ["**/vitest-setup*.ts", "**/vitest.config.ts", "**/vitest.*.config.ts"], "linter": {"rules": {"style": {"noNonNullAssertion": "off"}, "correctness": {"noUndeclaredVariables": "off"}}}}]}