{"name": "dental-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "biome lint --files-ignore-unknown=true --write", "format": "biome format --write", "imports:fix": "biome check --fix --unsafe .", "typecheck": "tsc --noEmit", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:studio": "pnpm prisma studio", "prisma:seed": "pnpm dlx tsx prisma/seed.ts", "postinstall": "prisma generate", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage", "migrate:uuid": "tsx scripts/data-migration/migrate-to-uuid.ts", "migrate:validate": "tsx scripts/data-migration/validate-migration.ts", "migrate:rollback": "tsx scripts/data-migration/rollback-uuid-migration.ts", "test:integration": "vitest --config vitest.integration.config.ts", "biome:check": "biome check .", "biome:fix": "biome check --write .", "biome:format": "biome format --write .", "biome:lint": "biome lint .", "pre-commit": "biome check --write . && pnpm test", "code-quality": "biome check . && pnpm test && pnpm build", "prepare": "husky"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@base-ui-components/react": "1.0.0-alpha.8", "@googleapis/drive": "^12.1.0", "@googleapis/sheets": "^9.8.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "boxen": "^8.0.1", "chalk": "^4.1.2", "class-variance-authority": "^0.7.1", "cli-table3": "^0.6.5", "clsx": "^2.1.1", "cmdk": "^1.1.1", "commander": "^11.1.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^4.21.2", "fastmcp": "^1.27.7", "figlet": "^1.8.1", "framer-motion": "^12.15.0", "fuse.js": "^7.1.0", "googleapis": "^148.0.0", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "inquirer": "^12.6.3", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.4.3", "lucide-react": "^0.510.0", "motion": "^12.15.0", "next": "15.3.2", "next-themes": "^0.4.6", "openai": "^4.104.0", "ora": "^8.2.0", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "recharts": "^2.15.3", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.25.36", "zustand": "^5.0.5"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@google/clasp": "3.0.5-alpha", "@stagewise/toolbar-next": "^0.1.2", "@tailwindcss/postcss": "^4.1.8", "@testing-library/jest-dom": "^6.6.3", "@types/node": "^20.17.52", "@types/pg": "^8.15.2", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/uuid": "^10.0.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "pg": "^8.16.0", "prisma": "^6.8.2", "tailwindcss": "^4.1.8", "tsx": "^4.19.4", "tw-animate-css": "^1.3.2", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39", "type": "module"}